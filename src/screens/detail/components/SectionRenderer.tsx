import React from "react"
import { LoanDetailSectionItem, LoanDetailSectionType } from "../types"
import {
  BasicSection,
  LoanTabsSection,
  LoanAttributesSection,
  LoanActionButtonsSection,
} from "./sections"
import { SellLoanButton } from "@/screens/shared/components"

interface SectionRendererProps {
  item: LoanDetailSectionItem
}

const SectionRenderer: React.FC<SectionRendererProps> = ({ item }) => {
  switch (item.type) {
    case LoanDetailSectionType.BASIC:
      return <BasicSection item={item} />
    case LoanDetailSectionType.LOAN_TABS:
      return <LoanTabsSection item={item} />
    case LoanDetailSectionType.LOAN_ATTRIBUTES:
      return <LoanAttributesSection item={item} />
    case LoanDetailSectionType.LOAN_ACTION_BUTTONS:
      return <SellLoanButton loan={item.loan} />
    default:
      return null
  }
}

export default SectionRenderer
