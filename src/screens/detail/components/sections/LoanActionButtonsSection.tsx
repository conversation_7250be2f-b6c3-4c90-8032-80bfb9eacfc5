import React from "react"
import { LoanActionButtonsSectionItem } from "src/screens/detail/types"
import LoanActionButtonsView from "../LoanActionButtonsView"

interface LoanActionButtonsSectionProps {
  item: LoanActionButtonsSectionItem
}

const LoanActionButtonsSection: React.FC<LoanActionButtonsSectionProps> = React.memo(({ item }) => {
  const { loan } = item

  return <LoanActionButtonsView loan={loan} />
})

LoanActionButtonsSection.displayName = "LoanActionButtonsSection"

export default LoanActionButtonsSection
