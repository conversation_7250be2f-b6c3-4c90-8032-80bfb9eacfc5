import React from "react"
import { useTranslation } from "react-i18next"
import { useAccount } from "wagmi"
import { MortgageLoan } from "src/service/types"
import { PrimaryButton } from "@/components/Button"
import { CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE } from "src/config/env"
import { CustomPressable } from "@/components"
import SellLoanModal from "./SellLoanModal"

interface SellLoanButtonProps {
  loan: MortgageLoan
  buttonContent?: React.ReactNode
  onRefresh?: () => void
}

export const SellLoanButton: React.FC<SellLoanButtonProps> = React.memo(
  ({ loan, buttonContent, onRefresh }) => {
    const { t } = useTranslation()
    const [isShow, setIsShow] = React.useState<boolean>(false)
    const { address } = useAccount()

    const handleSell = React.useCallback(() => {
      setIsShow(true)
    }, [])

    const handleClose = React.useCallback(() => {
      setIsShow(false)
    }, [])

    if (!address || !CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE) {
      return null
    }

    return (
      <>
        {buttonContent ? (
          <CustomPressable onPress={handleSell}>{buttonContent}</CustomPressable>
        ) : (
          <PrimaryButton title={t("Sell Loan")} onPress={handleSell} />
        )}
        <SellLoanModal loan={loan} isShow={isShow} onClose={handleClose} onRefresh={onRefresh} />
      </>
    )
  },
)
