import React from "react"
import { StyleSheet, View, Image, Text } from "react-native"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { BaseModal, LabelView } from "@/components"
import { InputField } from "@/components"
import { PrimaryButton } from "@/components/Button"
import { Controller } from "react-hook-form"
import { MortgageLoan } from "@/service/types"
import { textStyles, viewStyles } from "@/config/styles"
import icBsc from "assets/images/ic_bsc.png"
import { useSellLoan } from "../hooks/useSellLoan"

interface SellLoanModalProps {
  loan: MortgageLoan
  isShow: boolean
  onClose: () => void
  onRefresh?: () => void
}

interface RowItemViewProps {
  label: string
  value: string
  tokenImageUrl: string
}

const RowItemView: React.FC<RowItemViewProps> = ({ label, value, tokenImageUrl }) => {
  return (
    <View style={styles.rowItemContainer}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.amountContainer}>
        <Text style={textStyles.SSemiBold}>{value}</Text>
        <Image source={{ uri: tokenImageUrl }} style={viewStyles.size14Icon} />
      </View>
    </View>
  )
}

const SellLoanModal: React.FC<SellLoanModalProps> = React.memo(
  ({ isShow, loan, onRefresh, onClose }) => {
    const { t } = useTranslation()
    const {
      isLoading,
      form,
      formatedPrincipal,
      formattedMortgageAmount,
      tokenSymbol,
      tokenImageUrl,
      chainName,
      isSellingPositive,
      onSubmit,
      resetForm,
    } = useSellLoan(loan, onRefresh)

    const closeAndReset = React.useCallback(() => {
      resetForm()
      onClose()
    }, [resetForm, onClose])

    return (
      <BaseModal
        visible={isShow}
        isShowCloseIcon={true}
        isDisableClose={isLoading}
        title={t("Sell Loan")}
        onClose={closeAndReset}
      >
        <View style={styles.modalContent}>
          <View style={styles.row}>
            <LabelView label={t("Sell price")} />
            <Controller
              control={form.control}
              name="sellPrice"
              render={({ field: { onChange, onBlur, value } }) => (
                <View style={styles.sellRow}>
                  <InputField
                    value={value}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    type={"number"}
                    placeholder="0.00"
                    style={styles.sellPrice}
                    error={form.formState.errors.sellPrice?.message}
                  />
                  <Image source={{ uri: tokenImageUrl }} style={styles.iconRight} />
                </View>
              )}
            />
          </View>
          <View style={styles.rowItemContainer}>
            <Text style={[textStyles.SMedium, styles.label]}>{t("Network")}</Text>
            <View style={styles.amountContainer}>
              <Image source={icBsc} style={viewStyles.size16Icon} />
              <Text style={textStyles.SSemiBold}>{chainName}</Text>
            </View>
          </View>
          <RowItemView label={t("Tolkens")} value={tokenSymbol} tokenImageUrl={tokenImageUrl} />
          <RowItemView
            label={t("Protocal fee")}
            value={formatedPrincipal}
            tokenImageUrl={tokenImageUrl}
          />
          <RowItemView
            label={t("Total Fee")}
            value={formattedMortgageAmount}
            tokenImageUrl={tokenImageUrl}
          />

          <PrimaryButton
            title={t("Sell")}
            onPress={form.handleSubmit(onSubmit)}
            style={styles.sellLoanButton}
            isLoading={isLoading}
            enabled={isSellingPositive}
          />
        </View>
      </BaseModal>
    )
  },
)

SellLoanModal.displayName = "SellLoanModal"

const styles = StyleSheet.create({
  modalContent: {
    width: "100%",
    flex: 1,
  },
  row: {
    width: "100%",
    marginTop: 12,
  },
  sellPrice: {
    width: "100%",
    borderRadius: 6,
    backgroundColor: Colors.PalleteBlack,
  },
  sellLoanButton: {
    marginTop: 40,
  },
  iconRight: {
    ...viewStyles.size14Icon,
    position: "absolute",
    right: 8,
    transform: [{ translateY: 12 }],
  },
  rowItemContainer: {
    width: "100%",
    marginTop: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    alignContent: "center",
    gap: 4,
  },
  label: {
    ...textStyles.MMedium,
    color: Colors.Neutral500,
  },
  amountContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  sellRow: {
    marginTop: 4,
  },
})

export default SellLoanModal
